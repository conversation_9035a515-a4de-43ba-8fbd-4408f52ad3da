0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.14.0
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/home/<USER>/.npm-global/etc/npmrc
6 verbose title npm start
7 verbose argv "start"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-10T21_54_05_095Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-07-10T21_54_05_095Z-debug-0.log
10 silly logfile done cleaning log files
11 http fetch GET 200 https://registry.npmjs.org/npm 696ms
