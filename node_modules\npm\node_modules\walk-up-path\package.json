{"name": "walk-up-path", "version": "3.0.1", "files": ["dist"], "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}, "import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}}}, "description": "Given a path string, return a generator that walks up the path, emitting each dirname.", "repository": {"type": "git", "url": "git+https://github.com/isaacs/walk-up-path"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "devDependencies": {"@types/node": "^18.15.5", "@types/tap": "^15.0.8", "c8": "^7.13.0", "eslint-config-prettier": "^8.8.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}}